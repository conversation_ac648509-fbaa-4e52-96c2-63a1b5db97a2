{"name": "caza-frontend", "version": "0.1.0", "private": true, "license": "MIT", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "fix-all": "npm run format && npm run lint:fix"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "autoprefixer": "^10.4.21", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "lucide-react": "^0.525.0", "next": "15.4.2", "postcss": "^8.5.6", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "react-i18next": "^15.6.1", "sonner": "^2.0.6", "swr": "^2.3.4", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "eslint": "^9", "eslint-config-next": "15.4.2", "prettier": "^3.6.2", "typescript": "^5"}}