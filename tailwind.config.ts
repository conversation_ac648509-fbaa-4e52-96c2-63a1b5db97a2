import type { Config } from 'tailwindcss';

const config: Config = {
  darkMode: ['class', 'class'],
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        // Cores do sistema (shadcn/ui)
        background: 'rgb(var(--background))',
        foreground: 'rgb(var(--foreground))',
        card: {
          DEFAULT: 'rgb(var(--card))',
          foreground: 'rgb(var(--card-foreground))',
        },
        popover: {
          DEFAULT: 'rgb(var(--popover))',
          foreground: 'rgb(var(--popover-foreground))',
        },
        primary: {
          DEFAULT: 'rgb(var(--primary))',
          foreground: 'rgb(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'rgb(var(--secondary))',
          foreground: 'rgb(var(--secondary-foreground))',
        },
        muted: {
          DEFAULT: 'rgb(var(--muted))',
          foreground: 'rgb(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'rgb(var(--accent))',
          foreground: 'rgb(var(--accent-foreground))',
        },
        destructive: {
          DEFAULT: 'rgb(var(--destructive))',
          foreground: 'rgb(var(--destructive-foreground))',
        },
        border: 'rgb(var(--border))',
        input: 'rgb(var(--input))',
        ring: 'rgb(var(--ring))',
        chart: {
          '1': 'rgb(var(--chart-1))',
          '2': 'rgb(var(--chart-2))',
          '3': 'rgb(var(--chart-3))',
          '4': 'rgb(var(--chart-4))',
          '5': 'rgb(var(--chart-5))',
        },

        // Cores customizadas da paleta - Primary (Azul) - 4 tons
        brand: {
          50: 'rgb(var(--primary-50))', // #E7EDF5
          100: 'rgb(var(--primary-100))', // #BDD6E5
          200: 'rgb(var(--primary-200))', // #809FB8
          500: 'rgb(var(--primary-500))', // #152F4E
        },

        // Cores customizadas da paleta - Secondary (Marrom) - 4 tons
        earth: {
          50: 'rgb(var(--secondary-50))', // #F4F0EB
          100: 'rgb(var(--secondary-100))', // #9B6F53
          200: 'rgb(var(--secondary-200))', // #7D5638
          500: 'rgb(var(--secondary-500))', // #623E24
        },

        // Cores de estado - Danger (Vermelho) - 4 tons
        danger: {
          50: 'rgb(var(--danger-50))', // #EA3A3D
          100: 'rgb(var(--danger-100))', // #EE6164
          200: 'rgb(var(--danger-200))', // #D00004
          500: 'rgb(var(--danger-500))', // #B20419
        },

        // Cores de estado - Success (Verde) - 4 tons
        success: {
          50: 'rgb(var(--success-50))', // #C8CBB3
          100: 'rgb(var(--success-100))', // #9CA279
          200: 'rgb(var(--success-200))', // #767738
          500: 'rgb(var(--success-500))', // #5F6328
        },

        // Cores de estado - Warning (Amarelo/Laranja) - 4 tons
        warning: {
          50: 'rgb(var(--warning-50))', // #E5D8C2
          100: 'rgb(var(--warning-100))', // #C8AA7A
          200: 'rgb(var(--warning-200))', // #C69555
          500: 'rgb(var(--warning-500))', // #B88330
        },

        // Cores de estado - Info (Azul informativo) - 1 tom
        info: {
          500: 'rgb(var(--info-500))', // #3B82F6
        },

        // Cores neutras - Gray - 4 tons
        gray: {
          50: 'rgb(var(--gray-50))', // #F9FAFB
          100: 'rgb(var(--gray-100))', // #F3F4F6
          200: 'rgb(var(--gray-200))', // #E5E7EB
          500: 'rgb(var(--gray-500))', // #6B7280
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      fontFamily: {
        montserrat: [
          'var(--font-montserrat)',
          'system-ui',
          '-apple-system',
          'BlinkMacSystemFont',
          'Segoe UI',
          'Roboto',
          'sans-serif',
        ],
        sans: [
          'var(--font-montserrat)',
          'system-ui',
          '-apple-system',
          'BlinkMacSystemFont',
          'Segoe UI',
          'Roboto',
          'sans-serif',
        ],
      },
      screens: {
        xs: '475px',
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
};

export default config;
