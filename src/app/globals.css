@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Fontes */
    --font-montserrat:
      '<PERSON><PERSON><PERSON>', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI',
      Roboto, sans-serif;

    /* Cores principais da paleta - Primary (Azul) - 4 tons */
    --primary-50: 231 237 245; /* #E7EDF5 - azul 4 */
    --primary-100: 189 214 229; /* #BDD6E5 - azul 3 */
    --primary-200: 128 159 184; /* #809FB8 - azul 2 */
    --primary-500: 21 47 78; /* #152F4E - azul 1 */

    /* Cores secundárias da paleta - Secondary (Marrom) - 4 tons */
    --secondary-50: 244 240 235; /* #F4F0EB - marron 4 */
    --secondary-100: 155 111 83; /* #9B6F53 - marron 3 */
    --secondary-200: 125 86 56; /* #7D5638 - marron 2 */
    --secondary-500: 98 62 36; /* #623E24 - marron 1 */

    /* Cores de estado - Danger (Vermelho) - 4 tons */
    --danger-50: 234 58 61; /* #EA3A3D - vermelho 4 */
    --danger-100: 238 97 100; /* #EE6164 - vermelho 3 */
    --danger-200: 208 0 4; /* #D00004 - vermelho 2 */
    --danger-500: 178 4 25; /* #B20419 - vermelho 1 */

    /* Cores de estado - Success (Verde) - 4 tons */
    --success-50: 200 203 179; /* #C8CBB3 - verde 4 */
    --success-100: 156 162 121; /* #9CA279 - verde 3 */
    --success-200: 118 119 56; /* #767738 - verde 2 */
    --success-500: 95 99 40; /* #5F6328 - verde 1 */

    /* Cores de estado - Warning (Amarelo/Laranja) - 4 tons */
    --warning-50: 229 216 194; /* #E5D8C2 - amarelo 4 */
    --warning-100: 200 170 122; /* #C8AA7A - amarelo 3 */
    --warning-200: 198 149 85; /* #C69555 - amarelo 2 */
    --warning-500: 184 131 48; /* #B88330 - amarelo 1 */

    /* Cores de estado - Info (Azul informativo) - 1 tom */
    --info-500: 59 130 246; /* #3B82F6 - azul info escuro */

    /* Cores neutras - Gray - 4 tons */
    --gray-50: 249 250 251; /* #F9FAFB - cinza muito claro */
    --gray-100: 243 244 246; /* #F3F4F6 - cinza claro */
    --gray-200: 229 231 235; /* #E5E7EB - cinza médio */
    --gray-500: 107 114 128; /* #6B7280 - cinza escuro */

    /* Cores do sistema (baseadas na paleta) */
    --background: 249 245 241; /* Background principal - bege claro */
    --foreground: 21 47 78; /* Texto principal - azul escuro */
    --card: 255 255 255; /* Cards - branco para contraste */
    --card-foreground: 21 47 78; /* Texto em cards */
    --popover: 255 255 255; /* Popovers - branco para contraste */
    --popover-foreground: 21 47 78; /* Texto em popovers */
    --primary: 21 47 78; /* Cor primária - azul escuro */
    --primary-foreground: 255 255 255; /* Texto sobre primária - branco */
    --secondary: 98 62 36; /* Cor secundária - marrom escuro */
    --secondary-foreground: 255 255 255; /* Texto sobre secundária - branco */
    --muted: 231 237 245; /* Background muted - azul muito claro */
    --muted-foreground: 128 159 184; /* Texto muted - azul médio */
    --accent: 189 214 229; /* Background accent - azul claro (primary-100) */
    --accent-foreground: 21 47 78; /* Texto accent - azul escuro */
    --destructive: 178 4 25; /* Cor destrutiva - vermelho escuro */
    --destructive-foreground: 255 255 255; /* Texto sobre destrutiva - branco */
    --border: 128 159 184; /* Bordas - azul médio */
    --input: 128 159 184; /* Inputs - azul médio */
    --ring: 21 47 78; /* Ring focus - azul escuro */
    --chart-1: 21 47 78; /* Chart cor 1 - azul escuro */
    --chart-2: 98 62 36; /* Chart cor 2 - marrom escuro */
    --chart-3: 95 99 40; /* Chart cor 3 - verde escuro */
    --chart-4: 184 131 48; /* Chart cor 4 - amarelo escuro */
    --chart-5: 178 4 25; /* Chart cor 5 - vermelho escuro */
    --radius: 0.5rem;
  }

  .dark {
    /* Cores principais da paleta (modo escuro) - 4 tons */
    --primary-50: 21 47 78; /* #152F4E - azul 1 */
    --primary-100: 128 159 184; /* #809FB8 - azul 2 */
    --primary-200: 189 214 229; /* #BDD6E5 - azul 3 */
    --primary-500: 231 237 245; /* #E7EDF5 - azul 4 */

    --secondary-50: 98 62 36; /* #623E24 - marron 1 */
    --secondary-100: 125 86 56; /* #7D5638 - marron 2 */
    --secondary-200: 155 111 83; /* #9B6F53 - marron 3 */
    --secondary-500: 244 240 235; /* #F4F0EB - marron 4 */

    /* Cores do sistema (modo escuro) */
    --background: 21 47 78; /* Background escuro - azul escuro */
    --foreground: 231 237 245; /* Texto claro - azul claro */
    --card: 30 40 60; /* Cards escuros - azul mais escuro */
    --card-foreground: 231 237 245; /* Texto em cards */
    --popover: 30 40 60; /* Popovers escuros */
    --popover-foreground: 231 237 245; /* Texto em popovers */
    --primary: 231 237 245; /* Cor primária - azul claro */
    --primary-foreground: 21 47 78; /* Texto sobre primária - azul escuro */
    --secondary: 244 240 235; /* Cor secundária - marrom claro */
    --secondary-foreground: 98 62 36; /* Texto sobre secundária - marrom escuro */
    --muted: 128 159 184; /* Background muted - azul médio */
    --muted-foreground: 231 237 245; /* Texto muted - azul claro */
    --accent: 98 62 36; /* Background accent - marrom escuro */
    --accent-foreground: 244 240 235; /* Texto accent - marrom claro */
    --destructive: 255 107 107; /* Cor destrutiva - vermelho claro */
    --destructive-foreground: 21 47 78; /* Texto sobre destrutiva - azul escuro */
    --border: 128 159 184; /* Bordas - azul médio */
    --input: 128 159 184; /* Inputs - azul médio */
    --ring: 231 237 245; /* Ring focus - azul claro */
    --chart-1: 231 237 245; /* Chart cor 1 - azul claro */
    --chart-2: 244 240 235; /* Chart cor 2 - marrom claro */
    --chart-3: 200 203 179; /* Chart cor 3 - verde claro */
    --chart-4: 229 216 194; /* Chart cor 4 - amarelo claro */
    --chart-5: 234 58 61; /* Chart cor 5 - vermelho claro */
  }
}

@layer base {
  body {
    @apply bg-background text-foreground;
    font-family:
      var(--font-montserrat),
      system-ui,
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      Roboto,
      sans-serif;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-semibold tracking-tight;
  }

  h1 {
    @apply text-4xl lg:text-5xl;
  }

  h2 {
    @apply text-3xl lg:text-4xl;
  }

  h3 {
    @apply text-2xl lg:text-3xl;
  }

  h4 {
    @apply text-xl lg:text-2xl;
  }

  h5 {
    @apply text-lg lg:text-xl;
  }

  h6 {
    @apply text-base lg:text-lg;
  }

  /* Scrollbar personalizada */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }

  /* Focus ring personalizado */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
