import Link from 'next/link';

export default function DesignSystemPage() {
  return (
    <div className="container mx-auto p-6">
      <div className="text-center mb-8">
        <h1 className="text-[32px] leading-[34px] font-bold mb-4">
          Sistema de Design
        </h1>
        <p className="text-[16px] leading-[18px] font-normal text-muted-foreground">
          Visualize e teste todos os elementos do sistema de design
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl mx-auto">
        <Link
          href="/colors"
          className="p-6 bg-card border border-border rounded-lg hover:bg-accent hover:border-accent-foreground transition-colors"
        >
          <h2 className="text-[22px] leading-[24px] font-bold mb-2">
            Paleta de Cores
          </h2>
          <p className="text-[14px] leading-[16px] font-normal text-muted-foreground">
            Visualize e teste todas as cores do sistema de design
          </p>
        </Link>

        <Link
          href="/typography"
          className="p-6 bg-card border border-border rounded-lg hover:bg-accent hover:border-accent-foreground transition-colors"
        >
          <h2 className="text-[22px] leading-[24px] font-bold mb-2">
            Tipografia
          </h2>
          <p className="text-[14px] leading-[16px] font-normal text-muted-foreground">
            Visualize e teste todos os estilos de tipografia
          </p>
        </Link>
      </div>
    </div>
  );
}
