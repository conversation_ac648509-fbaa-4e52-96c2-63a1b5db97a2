'use client';

import { ArrowLeft, Plus } from 'lucide-react';
import { useRouter } from 'next/navigation';

import { PermissionGuard } from '@/components/auth/permission-guard';
import { Button } from '@/components/ui/button';
import { DataTable, PermissionBadges } from '@/components/ui/data-table';
import { useEntityPage } from '@/hooks/use-entity-page';
import { useTranslation } from '@/hooks/use-translation';
import { User } from '@/lib/types';

export default function UsersPage() {
  const { t } = useTranslation();
  const router = useRouter();

  const {
    data: users,
    isLoading,
    isDeleting,
    canView,
    handleDelete,
    user,
    authLoading,
    pageProps,
  } = useEntityPage('users');

  const handleEdit = (_userItem: User) => {
    // TODO: Implementar edição
  };

  const renderUserItem = (userItem: User) => (
    <>
      <h3 className="font-semibold">{userItem.fullName}</h3>
      <p className="text-sm text-muted-foreground">{userItem.email}</p>
      <p className="text-xs text-muted-foreground">
        {t('users.group')}:{' '}
        {userItem.userGroup?.name || t('common.notAvailable')}
      </p>
      <div className="mt-2">
        <PermissionBadges permissions={userItem.userGroup?.permissions || []} />
      </div>
    </>
  );

  if (authLoading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  if (!canView) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      <header className="border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/dashboard')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                {t('common.back')}
              </Button>
              <div>
                <h1 className="text-2xl font-bold">{pageProps.title}</h1>
                <p className="text-muted-foreground">{pageProps.description}</p>
              </div>
            </div>

            <PermissionGuard permission={pageProps.permissions.create}>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                {pageProps.createButton}
              </Button>
            </PermissionGuard>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <DataTable<User>
          data={users}
          loading={isLoading}
          emptyMessage={pageProps.emptyMessage}
          loadingMessage={pageProps.loadingMessage}
          renderItem={renderUserItem}
          onEdit={handleEdit}
          onDelete={handleDelete}
          editPermission={pageProps.permissions.edit}
          deletePermission={pageProps.permissions.delete}
          isDeleting={isDeleting}
          getItemName={(user: User) => user.fullName}
        />
      </main>
    </div>
  );
}
