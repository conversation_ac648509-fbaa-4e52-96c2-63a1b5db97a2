import type { Metada<PERSON> } from 'next';
import { Montserrat } from 'next/font/google';

import { ErrorBoundary } from '@/components/error-boundary';
import { GlobalModal } from '@/components/global-modal';
import { AppProvider } from '@/components/providers/app-provider';
import { Toaster } from '@/components/ui/sonner';
import { AuthProvider } from '@/contexts/auth-context';
import { I18nProvider } from '@/contexts/i18n-context';
import { GlobalModalProvider } from '@/contexts/modal-context';
import { ThemeProvider } from '@/contexts/theme-context';
import { ToastProvider } from '@/contexts/toast-context';

import './globals.css';

const montserrat = Montserrat({
  variable: '--font-montserrat',
  subsets: ['latin'],
  weight: ['400', '700'],
});

export const metadata: Metadata = {
  title: 'Caza',
  description: 'Sistema de gestão Home Staging',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="pt" suppressHydrationWarning>
      <body className={`${montserrat.variable} antialiased`}>
        <I18nProvider>
          <ThemeProvider defaultTheme="system" storageKey="caza-ui-theme">
            <ToastProvider>
              <GlobalModalProvider>
                <AuthProvider>
                  <ErrorBoundary>
                    <AppProvider>{children}</AppProvider>
                    <GlobalModal />
                    <Toaster />
                  </ErrorBoundary>
                </AuthProvider>
              </GlobalModalProvider>
            </ToastProvider>
          </ThemeProvider>
        </I18nProvider>
      </body>
    </html>
  );
}
