'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

import { PermissionGuard } from '@/components/auth/permission-guard';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useAuth } from '@/contexts/auth-context';
import { useTranslation } from '@/hooks/use-translation';
import { entities } from '@/lib/entity-registry';

export default function DashboardPage() {
  const { user, logout } = useAuth();
  const { t } = useTranslation();
  const router = useRouter();

  // Redirecionar se não estiver logado
  useEffect(() => {
    if (!user) {
      router.push('/');
    }
  }, [user, router]);

  // Loading enquanto verifica autenticação
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold">{t('dashboard.title')}</h1>
          <p className="text-muted-foreground mt-2">
            {t('dashboard.welcome')}, {user.fullName}
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>{t('dashboard.overview')}</CardTitle>
              <CardDescription>{t('dashboard.statistics')}</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                {t('dashboard.recentProjects')}: 0
              </p>
              <p className="text-muted-foreground">
                {t('dashboard.recentClients')}: 0
              </p>
            </CardContent>
          </Card>

          <PermissionGuard permission={entities.users.permissions.view}>
            <Card>
              <CardHeader>
                <CardTitle>{t('users.title')}</CardTitle>
                <CardDescription>{t('users.manage')}</CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  className="w-full"
                  variant="outline"
                  onClick={() => router.push('/users')}
                >
                  {t('users.view')}
                </Button>
              </CardContent>
            </Card>
          </PermissionGuard>

          <PermissionGuard permission={entities.clients.permissions.view}>
            <Card>
              <CardHeader>
                <CardTitle>{t('clients.title')}</CardTitle>
                <CardDescription>{t('clients.create')}</CardDescription>
              </CardHeader>
              <CardContent>
                <Button className="w-full" variant="outline">
                  {t('clients.create')}
                </Button>
              </CardContent>
            </Card>
          </PermissionGuard>

          <PermissionGuard permission={entities.projects.permissions.view}>
            <Card>
              <CardHeader>
                <CardTitle>{t('projects.title')}</CardTitle>
                <CardDescription>{t('projects.create')}</CardDescription>
              </CardHeader>
              <CardContent>
                <Button className="w-full" variant="outline">
                  {t('projects.create')}
                </Button>
              </CardContent>
            </Card>
          </PermissionGuard>

          <Card>
            <CardHeader>
              <CardTitle>Permissões</CardTitle>
              <CardDescription>Suas permissões no sistema</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {user.userGroup?.permissions?.map((permission: string) => (
                  <Badge
                    key={permission}
                    variant="secondary"
                    className="mr-1 mb-1"
                  >
                    {permission}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
