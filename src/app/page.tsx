'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Eye, EyeOff } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { LanguageSelector } from '@/components/ui/language-selector';
import { Loading } from '@/components/ui/loading';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { useAuth } from '@/contexts/auth-context';
import { useToastContext } from '@/contexts/toast-context';
import { useTranslation } from '@/hooks/use-translation';

import { ApiError } from '@/lib/api';

const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
  rememberMe: z.boolean().optional(),
});

type LoginForm = z.infer<typeof loginSchema>;

// Brand Logo Component
const CazaLogo = () => (
  <div className="flex items-center gap-3">
    <div className="w-10 h-10 sm:w-12 sm:h-12 bg-primary rounded-full flex items-center justify-center">
      <span className="text-white font-bold text-base sm:text-lg">C</span>
    </div>
    <div>
      <h1 className="text-lg sm:text-xl font-bold text-foreground">
        CAZA DESIGN
      </h1>
      <p className="text-xs sm:text-sm text-muted-foreground">
        HOUSE INTO HOME
      </p>
    </div>
  </div>
);

// Decorative Caza Brand Element for Right Side
const CazaBrandElement = () => (
  <div className="flex items-center justify-center">
    <div className="text-4xl xl:text-6xl font-bold text-white/20 tracking-wider">
      CA<span className="text-white/40">Z</span>A
    </div>
  </div>
);

export default function LoginPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const { login, user } = useAuth();
  const { t } = useTranslation();
  const { showSuccess, showError } = useToastContext();
  const router = useRouter();

  // Redirecionar se já estiver logado
  useEffect(() => {
    if (user) {
      router.push('/dashboard');
    }
  }, [user, router]);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginForm>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = async (data: LoginForm) => {
    setIsLoading(true);
    try {
      await login(data.email, data.password, data.rememberMe);
      showSuccess(t('auth.loginSuccess'));
      router.push('/dashboard');
    } catch (error) {
      if (error instanceof ApiError) {
        // Usar a mensagem específica do backend se disponível
        const errorMessage = error.message || t('auth.invalidCredentials');
        showError(errorMessage);
      } else {
        showError(t('auth.invalidCredentials'));
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Redirecionar para dashboard se já estiver logado
  useEffect(() => {
    if (user) {
      router.push('/dashboard');
    }
  }, [user, router]);

  // Loading enquanto verifica se já está logado
  if (user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex">
      {/* Language and Theme Toggle - Top Right */}
      <div className="absolute top-4 right-4 z-10 flex items-center space-x-2">
        <LanguageSelector />
        <ThemeToggle />
      </div>

      {/* Left Side - Login Form */}
      <div className="flex-1 flex items-center justify-center bg-background p-4 sm:p-8 lg:p-16">
        <div className="w-full max-w-md space-y-6 sm:space-y-8">
          {/* Logo */}
          <CazaLogo />

          {/* Login Form */}
          <div className="space-y-6">
            <div className="space-y-2">
              <h2 className="text-[28px] sm:text-[32px] leading-[30px] sm:leading-[34px] font-bold text-foreground">
                {t('auth.signInTitle')}
              </h2>
              <p className="text-[14px] sm:text-[16px] leading-[16px] sm:leading-[18px] text-muted-foreground">
                {t('auth.signInSubtitle')}
              </p>
            </div>

            <form
              onSubmit={handleSubmit(onSubmit)}
              className="space-y-4"
              noValidate
            >
              {/* Email Field */}
              <div className="space-y-2">
                <Input
                  id="email"
                  type="email"
                  placeholder={t('auth.emailPlaceholder')}
                  {...register('email')}
                  className={`h-11 sm:h-12 text-sm ${
                    errors.email ? 'border-destructive' : 'border-border'
                  }`}
                  autoComplete="email"
                />
                {errors.email && (
                  <p className="text-sm text-destructive">
                    {t('forms.emailInvalid')}
                  </p>
                )}
              </div>

              {/* Password Field */}
              <div className="space-y-2">
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder={t('auth.passwordPlaceholder')}
                    {...register('password')}
                    className={`h-11 sm:h-12 text-sm pr-12 ${
                      errors.password ? 'border-destructive' : 'border-border'
                    }`}
                    autoComplete="current-password"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 text-muted-foreground hover:text-foreground"
                    onClick={() => setShowPassword(!showPassword)}
                    aria-label={
                      showPassword
                        ? t('auth.hidePassword')
                        : t('auth.showPassword')
                    }
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                {errors.password && (
                  <p className="text-sm text-destructive">
                    {t('forms.passwordTooShort')}
                  </p>
                )}
              </div>

              {/* Remember Me & Forgot Password */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <input
                    id="rememberMe"
                    type="checkbox"
                    {...register('rememberMe')}
                    className="h-4 w-4 rounded border-border text-primary focus:ring-primary focus:ring-offset-0"
                  />
                  <label
                    htmlFor="rememberMe"
                    className="text-sm text-foreground"
                  >
                    {t('auth.rememberMe')}
                  </label>
                </div>
                <button
                  type="button"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors"
                >
                  {t('auth.forgotPassword')}?
                </button>
              </div>

              {/* Sign In Button */}
              <Button
                type="submit"
                className="w-full h-11 sm:h-12 bg-primary hover:bg-primary/90 text-primary-foreground font-medium"
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <Loading />
                    {t('common.loading')}
                  </div>
                ) : (
                  t('auth.loginButton')
                )}
              </Button>
            </form>

            {/* Sign Up Link */}
            <div className="text-center">
              <p className="text-sm text-muted-foreground">
                {t('auth.createAccountText')}{' '}
                <button className="text-primary hover:text-primary/80 font-medium transition-colors">
                  {t('auth.signUpLink')}
                </button>
              </p>
            </div>
          </div>

          {/* Copyright */}
          <div className="pt-8">
            <p className="text-sm text-muted-foreground">
              {t('auth.copyrightText')}
            </p>
          </div>
        </div>
      </div>

      {/* Right Side - Welcome Section */}
      <div className="hidden lg:flex flex-1 bg-primary items-center justify-center p-8 xl:p-16 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary via-primary to-primary/90" />

        {/* Content */}
        <div className="relative z-10 text-center space-y-4 xl:space-y-6 max-w-lg">
          <h1 className="text-[36px] xl:text-[48px] leading-[40px] xl:leading-[52px] font-bold text-white">
            {t('auth.welcomeTitle')}
          </h1>
          <p className="text-[16px] xl:text-[18px] leading-[22px] xl:leading-[24px] text-white/80">
            {t('auth.welcomeSubtitle')}
          </p>

          {/* Decorative Brand Element */}
          <div className="pt-8 xl:pt-12">
            <CazaBrandElement />
          </div>
        </div>

        {/* Subtle background decoration */}
        <div className="absolute bottom-0 right-0 w-48 xl:w-64 h-48 xl:h-64 bg-white/5 rounded-full -translate-y-24 xl:-translate-y-32 translate-x-24 xl:translate-x-32" />
        <div className="absolute top-0 left-0 w-32 xl:w-48 h-32 xl:h-48 bg-white/5 rounded-full -translate-y-16 xl:-translate-y-24 -translate-x-16 xl:-translate-x-24" />
      </div>
    </div>
  );
}
