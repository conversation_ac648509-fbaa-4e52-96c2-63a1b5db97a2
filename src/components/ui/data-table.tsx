'use client';

import { Edit, Trash2 } from 'lucide-react';
import { ReactNode, useCallback, useState } from 'react';

import { PermissionGuard } from '@/components/auth/permission-guard';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ConfirmDialog } from '@/components/ui/confirm-dialog';
import { Loading } from '@/components/ui/loading';
import { useTranslation } from '@/hooks/use-translation';

interface DataTableProps<T> {
  data: T[];
  loading: boolean;
  emptyMessage: string;
  loadingMessage: string;
  renderItem: (item: T) => ReactNode;
  onEdit?: (item: T) => void;
  onDelete?: (item: T) => void;
  editPermission?: string;
  deletePermission?: string;
  isDeleting?: boolean;
  getItemName?: (item: T) => string; // Função para obter o nome do item para o dialog
  _entityName?: string; // Nome da entidade para mensagens de confirmação
}

export function DataTable<T>({
  data,
  loading,
  emptyMessage,
  renderItem,
  onEdit,
  onDelete,
  editPermission,
  deletePermission,
  isDeleting = false,
  getItemName = (item: any) => item.name || item.title || 'Item',
  _entityName = 'item',
}: DataTableProps<T>) {
  const { t } = useTranslation();
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean;
    item: T | null;
  }>({
    open: false,
    item: null,
  });

  const handleDeleteClick = useCallback(
    (item: T) => {
      if (onDelete) {
        setConfirmDialog({
          open: true,
          item,
        });
      }
    },
    [onDelete]
  );

  const handleConfirmDelete = useCallback(() => {
    if (confirmDialog.item && onDelete) {
      onDelete(confirmDialog.item);
    }
    setConfirmDialog({
      open: false,
      item: null,
    });
  }, [confirmDialog.item, onDelete]);

  const handleCancelDelete = useCallback(() => {
    setConfirmDialog({
      open: false,
      item: null,
    });
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loading />
      </div>
    );
  }

  if (!Array.isArray(data) || data.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">{emptyMessage}</p>
      </div>
    );
  }

  return (
    <>
      <div className="grid gap-4">
        {data.map((item, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">{renderItem(item)}</div>
                <div className="flex items-center space-x-2">
                  {editPermission && onEdit && (
                    <PermissionGuard permission={editPermission}>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onEdit(item)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </PermissionGuard>
                  )}

                  {deletePermission && onDelete && (
                    <PermissionGuard permission={deletePermission}>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteClick(item)}
                        disabled={isDeleting}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </PermissionGuard>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* ConfirmDialog integrado */}
      <ConfirmDialog
        open={confirmDialog.open}
        title={t('common.confirmDelete.title')}
        description={t('common.confirmDelete.description').replace(
          '{name}',
          confirmDialog.item ? getItemName(confirmDialog.item) : 'Item'
        )}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        loading={isDeleting}
      />
    </>
  );
}

// Componente para renderizar badges de permissões
export function PermissionBadges({ permissions }: { permissions: string[] }) {
  return (
    <>
      {permissions?.map(permission => (
        <Badge key={permission} variant="secondary" className="text-xs">
          {permission}
        </Badge>
      ))}
    </>
  );
}
