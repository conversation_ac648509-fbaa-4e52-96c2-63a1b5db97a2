'use client';

import { <PERSON>u, X } from 'lucide-react';
import { ReactNode, useState } from 'react';

import { Sidebar } from '@/components/navigation/sidebar';
import { Button } from '@/components/ui/button';
import { LanguageSelector } from '@/components/ui/language-selector';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { SidebarProvider, useSidebar } from '@/contexts/sidebar-context';
import { cn } from '@/lib/utils';

interface DashboardLayoutProps {
  children: ReactNode;
  className?: string;
}

function DashboardLayoutContent({ children, className }: DashboardLayoutProps) {
  const { isCollapsed, toggle } = useSidebar();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <div className="min-h-screen bg-background">
      {/* Mobile menu overlay */}
      {mobileMenuOpen && (
        <div
          className="fixed inset-0 z-50 bg-black/50 lg:hidden"
          onClick={() => setMobileMenuOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={cn(
        'lg:block',
        mobileMenuOpen ? 'block' : 'hidden'
      )}>
        <Sidebar />
      </div>

      {/* Mobile sidebar */}
      <div className={cn(
        'fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out lg:hidden',
        mobileMenuOpen ? 'translate-x-0' : '-translate-x-full'
      )}>
        <Sidebar />
        <Button
          variant="ghost"
          size="icon"
          className="absolute right-4 top-4 text-white hover:bg-white/10"
          onClick={() => setMobileMenuOpen(false)}
        >
          <X className="h-5 w-5" />
        </Button>
      </div>

      {/* Main content */}
      <div
        className={cn(
          'transition-all duration-300 ease-in-out',
          {
            'lg:ml-16': isCollapsed,
            'lg:ml-64': !isCollapsed,
            'ml-0': true, // Mobile always starts at 0
          }
        )}
      >
        {/* Top header */}
        <header className="sticky top-0 z-30 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex h-16 items-center justify-between px-4 sm:px-6">
            <div className="flex items-center space-x-4">
              {/* Mobile menu button */}
              <Button
                variant="ghost"
                size="icon"
                className="lg:hidden"
                onClick={() => setMobileMenuOpen(true)}
              >
                <Menu className="h-5 w-5" />
              </Button>

              {/* Desktop sidebar toggle */}
              <Button
                variant="ghost"
                size="icon"
                className="hidden lg:flex"
                onClick={toggle}
              >
                <Menu className="h-5 w-5" />
              </Button>

              {/* Breadcrumb or page title can go here */}
              <div className="hidden sm:block">
                <h1 className="text-lg font-semibold">Dashboard</h1>
              </div>
            </div>

            {/* Right side controls */}
            <div className="flex items-center space-x-2">
              <LanguageSelector />
              <ThemeToggle />
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className={cn('flex-1', className)}>
          {children}
        </main>
      </div>
    </div>
  );
}

export function DashboardLayout({ children, className }: DashboardLayoutProps) {
  return (
    <SidebarProvider>
      <DashboardLayoutContent className={className}>
        {children}
      </DashboardLayoutContent>
    </SidebarProvider>
  );
}
