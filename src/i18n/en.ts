const enTranslations = {
  // Authentication
  auth: {
    login: 'Login',
    logout: 'Logout',
    register: 'Register',
    email: 'Email',
    password: 'Password',
    confirmPassword: 'Confirm password',
    forgotPassword: 'Forgot password',
    rememberMe: 'Remember me',
    loginTitle: 'Sign in to your account',
    registerTitle: 'Create new account',
    loginButton: 'Sign in',
    registerButton: 'Register',
    invalidCredentials: 'Invalid credentials',
    accountCreated: 'Account created successfully',
    loginSuccess: 'Login successful',
    // New keys for redesigned login
    signInTitle: 'Sign In',
    signInSubtitle: 'Enter your email and password to login to your account.',
    emailPlaceholder: 'Email address',
    passwordPlaceholder: 'Password',
    createAccountText: 'Want to create an account?',
    signUpLink: 'Sign up',
    welcomeTitle: 'Welcome to Caza Design.',
    welcomeSubtitle: "Let's get you all set up so you can verify your personal account and begin setting up your profile",
    showPassword: 'Show password',
    hidePassword: 'Hide password',
    copyrightText: '© Caza Design 2025',
  },

  // General
  common: {
    save: 'Save',
    cancel: 'Cancel',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    search: 'Search',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    warning: 'Warning',
    info: 'Information',
    confirm: 'Confirm',
    close: 'Close',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    yes: 'Yes',
    no: 'No',
    name: 'Name',
    description: 'Description',
    status: 'Status',
    actions: 'Actions',
    createdAt: 'Created at',
    updatedAt: 'Updated at',
    notAvailable: 'N/A',
    errors: {
      invalidData: 'Invalid data in request',
      serverError: 'Internal server error',
      networkError: 'Connection error. Check your internet.',
      unauthorized: 'Session expired. Please login again.',
      forbidden: "You don't have permission for this action",
      badRequest: 'Bad request',
      conflict: 'Data conflict',
      tooManyRequests: 'Too many requests. Try again in a few moments.',
      serviceUnavailable: 'Service temporarily unavailable',
    },
    confirmDelete: {
      title: 'Confirm deletion',
      description:
        'Are you sure you want to delete {name}? This action cannot be undone.',
    },
  },

  // Forms
  forms: {
    required: 'This field is required',
    emailInvalid: 'Invalid email',
    passwordTooShort: 'Password must be at least 6 characters',
    passwordMismatch: 'Passwords do not match',
    fieldRequired: 'Required field',
  },

  // Themes
  theme: {
    light: 'Light',
    dark: 'Dark',
    system: 'System',
    toggleTheme: 'Toggle theme',
  },

  // Languages
  language: {
    portuguese: 'Portuguese',
    english: 'English',
    spanish: 'Spanish',
    selectLanguage: 'Select language',
  },

  // Dashboard
  dashboard: {
    title: 'Dashboard',
    welcome: 'Welcome',
    overview: 'Overview',
    recentProjects: 'Recent projects',
    recentClients: 'Recent clients',
    statistics: 'Statistics',
  },

  // Projects
  projects: {
    title: 'Projects',
    description: 'Manage system projects',
    create: 'Create project',
    edit: 'Edit project',
    delete: 'Delete project',
    empty: 'No projects found',
    loading: 'Loading projects...',
    messages: {
      delete: {
        confirm: 'Are you sure you want to delete this project?',
        success: 'Project deleted successfully',
      },
      create: {
        success: 'Project created successfully',
      },
      update: {
        success: 'Project updated successfully',
      },
    },
    errors: {
      noPermission: "You don't have permission to access this page",
      noDeletePermission: "You don't have permission to delete projects",
      notFound: 'Project not found',
      invalidData: 'Invalid data to delete project',
      deleteFailed: 'Error deleting project',
      operationFailed: 'Error executing operation',
    },
  },

  // Clients
  clients: {
    title: 'Clients',
    description: 'Manage system clients',
    create: 'Create client',
    edit: 'Edit client',
    delete: 'Delete client',
    empty: 'No clients found',
    loading: 'Loading clients...',
    messages: {
      delete: {
        confirm: 'Are you sure you want to delete this client?',
        success: 'Client deleted successfully',
      },
      create: {
        success: 'Client created successfully',
      },
      update: {
        success: 'Client updated successfully',
      },
    },
    errors: {
      noPermission: "You don't have permission to access this page",
      noDeletePermission: "You don't have permission to delete clients",
      notFound: 'Client not found',
      invalidData: 'Invalid data to delete client',
      deleteFailed: 'Error deleting client',
      operationFailed: 'Error executing operation',
    },
  },

  // Generic item
  item: {
    errors: {
      noPermission: "You don't have permission to access this resource",
      notFound: 'Item not found',
      operationFailed: 'Error executing operation',
    },
  },

  // Users
  users: {
    title: 'Users',
    description: 'Manage system users',
    view: 'View users',
    create: 'Create user',
    edit: 'Edit user',
    delete: 'Delete user',
    manage: 'Manage system users',
    group: 'Group',
    empty: 'No users found',
    loading: 'Loading users...',
    messages: {
      delete: {
        confirm: 'Are you sure you want to delete this user?',
        success: 'User deleted successfully',
      },
      create: {
        success: 'User created successfully',
      },
      update: {
        success: 'User updated successfully',
      },
    },
    errors: {
      noPermission: "You don't have permission to access this page",
      noDeletePermission: "You don't have permission to delete users",
      notFound: 'User not found',
      invalidData: 'Invalid data to delete user',
      deleteFailed: 'Error deleting user',
      operationFailed: 'Error executing operation',
    },
  },

  // Products (example of new entity)
  products: {
    title: 'Products',
    description: 'Manage system products',
    create: 'Create product',
    edit: 'Edit product',
    delete: 'Delete product',
    empty: 'No products found',
    loading: 'Loading products...',
    messages: {
      delete: {
        confirm: 'Are you sure you want to delete this product?',
        success: 'Product deleted successfully',
      },
      create: {
        success: 'Product created successfully',
      },
      update: {
        success: 'Product updated successfully',
      },
    },
    errors: {
      noPermission: "You don't have permission to access this page",
      noDeletePermission: "You don't have permission to delete products",
      notFound: 'Product not found',
      invalidData: 'Invalid data to delete product',
      deleteFailed: 'Error deleting product',
      operationFailed: 'Error executing operation',
    },
  },
};

export default enTranslations;
