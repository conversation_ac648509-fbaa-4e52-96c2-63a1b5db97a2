const esTranslations = {
  // Autenticación
  auth: {
    login: 'Iniciar sesión',
    logout: '<PERSON><PERSON><PERSON> sesión',
    register: 'Registrarse',
    email: '<PERSON>rreo electrón<PERSON>',
    password: 'Contrase<PERSON>',
    confirmPassword: 'Confirmar contraseña',
    forgotPassword: 'Olvidé mi contraseña',
    rememberMe: 'Recordarme',
    loginTitle: 'Iniciar sesión en tu cuenta',
    registerTitle: 'Crear nueva cuenta',
    loginButton: 'Iniciar sesión',
    registerButton: 'Registrarse',
    invalidCredentials: 'Credenciales inválidas',
    accountCreated: 'Cuenta creada exitosamente',
    loginSuccess: 'Inicio de sesión exitoso',
    // New keys for redesigned login
    signInTitle: 'Iniciar Sesión',
    signInSubtitle: 'Ingresa tu email y contraseña para acceder a tu cuenta.',
    emailPlaceholder: 'Dirección de email',
    passwordPlaceholder: 'Contrase<PERSON>',
    createAccountText: '¿Quieres crear una cuenta?',
    signUpLink: 'Regístrate',
    welcomeTitle: 'Bienvenido a Caza Design.',
    welcomeSubtitle: 'Configuremos todo para que puedas verificar tu cuenta personal y comenzar a configurar tu perfil',
    showPassword: 'Mostrar contraseña',
    hidePassword: 'Ocultar contraseña',
    copyrightText: '© Caza Design 2025',
  },

  // General
  common: {
    save: 'Guardar',
    cancel: 'Cancelar',
    delete: 'Eliminar',
    edit: 'Editar',
    add: 'Agregar',
    search: 'Buscar',
    loading: 'Cargando...',
    error: 'Error',
    success: 'Éxito',
    warning: 'Advertencia',
    info: 'Información',
    confirm: 'Confirmar',
    close: 'Cerrar',
    back: 'Volver',
    next: 'Siguiente',
    previous: 'Anterior',
    yes: 'Sí',
    no: 'No',
    name: 'Nombre',
    description: 'Descripción',
    status: 'Estado',
    actions: 'Acciones',
    createdAt: 'Creado en',
    updatedAt: 'Actualizado en',
    notAvailable: 'N/A',
    errors: {
      invalidData: 'Datos inválidos en la solicitud',
      serverError: 'Error interno del servidor',
      networkError: 'Error de conexión. Verifica tu internet.',
      unauthorized: 'Sesión expirada. Inicia sesión nuevamente.',
      forbidden: 'No tienes permiso para esta acción',
      badRequest: 'Solicitud incorrecta',
      conflict: 'Conflicto de datos',
      tooManyRequests:
        'Demasiadas solicitudes. Intenta nuevamente en unos momentos.',
      serviceUnavailable: 'Servicio temporalmente no disponible',
    },
    confirmDelete: {
      title: 'Confirmar eliminación',
      description:
        '¿Estás seguro de que quieres eliminar {name}? Esta acción no se puede deshacer.',
    },
  },

  // Formularios
  forms: {
    required: 'Este campo es obligatorio',
    emailInvalid: 'Correo electrónico inválido',
    passwordTooShort: 'La contraseña debe tener al menos 6 caracteres',
    passwordMismatch: 'Las contraseñas no coinciden',
    fieldRequired: 'Campo obligatorio',
  },

  // Temas
  theme: {
    light: 'Claro',
    dark: 'Oscuro',
    system: 'Sistema',
    toggleTheme: 'Cambiar tema',
  },

  // Idiomas
  language: {
    portuguese: 'Portugués',
    english: 'Inglés',
    spanish: 'Español',
    selectLanguage: 'Seleccionar idioma',
  },

  // Dashboard
  dashboard: {
    title: 'Dashboard',
    welcome: 'Bienvenido',
    overview: 'Resumen',
    recentProjects: 'Proyectos recientes',
    recentClients: 'Clientes recientes',
    statistics: 'Estadísticas',
  },

  // Proyectos
  projects: {
    title: 'Proyectos',
    description: 'Gestionar proyectos del sistema',
    create: 'Crear proyecto',
    edit: 'Editar proyecto',
    delete: 'Eliminar proyecto',
    empty: 'No se encontraron proyectos',
    loading: 'Cargando proyectos...',
    messages: {
      delete: {
        confirm: '¿Estás seguro de que quieres eliminar este proyecto?',
        success: 'Proyecto eliminado exitosamente',
      },
      create: {
        success: 'Proyecto creado exitosamente',
      },
      update: {
        success: 'Proyecto actualizado exitosamente',
      },
    },
    errors: {
      noPermission: 'No tienes permiso para acceder a esta página',
      noDeletePermission: 'No tienes permiso para eliminar proyectos',
      notFound: 'Proyecto no encontrado',
      invalidData: 'Datos inválidos para eliminar proyecto',
      deleteFailed: 'Error al eliminar proyecto',
      operationFailed: 'Error al ejecutar operación',
    },
  },

  // Clientes
  clients: {
    title: 'Clientes',
    description: 'Gestionar clientes del sistema',
    create: 'Crear cliente',
    edit: 'Editar cliente',
    delete: 'Eliminar cliente',
    empty: 'No se encontraron clientes',
    loading: 'Cargando clientes...',
    messages: {
      delete: {
        confirm: '¿Estás seguro de que quieres eliminar este cliente?',
        success: 'Cliente eliminado exitosamente',
      },
      create: {
        success: 'Cliente creado exitosamente',
      },
      update: {
        success: 'Cliente actualizado exitosamente',
      },
    },
    errors: {
      noPermission: 'No tienes permiso para acceder a esta página',
      noDeletePermission: 'No tienes permiso para eliminar clientes',
      notFound: 'Cliente no encontrado',
      invalidData: 'Datos inválidos para eliminar cliente',
      deleteFailed: 'Error al eliminar cliente',
      operationFailed: 'Error al ejecutar operación',
    },
  },

  // Item genérico
  item: {
    errors: {
      noPermission: 'No tienes permiso para acceder a este recurso',
      notFound: 'Elemento no encontrado',
      operationFailed: 'Error al ejecutar operación',
    },
  },

  // Usuarios
  users: {
    title: 'Usuarios',
    description: 'Gestionar usuarios del sistema',
    view: 'Ver usuarios',
    create: 'Crear usuario',
    edit: 'Editar usuario',
    delete: 'Eliminar usuario',
    manage: 'Gestionar usuarios del sistema',
    group: 'Grupo',
    empty: 'No se encontraron usuarios',
    loading: 'Cargando usuarios...',
    messages: {
      delete: {
        confirm: '¿Estás seguro de que quieres eliminar este usuario?',
        success: 'Usuario eliminado exitosamente',
      },
      create: {
        success: 'Usuario creado exitosamente',
      },
      update: {
        success: 'Usuario actualizado exitosamente',
      },
    },
    errors: {
      noPermission: 'No tienes permiso para acceder a esta página',
      noDeletePermission: 'No tienes permiso para eliminar usuarios',
      notFound: 'Usuario no encontrado',
      invalidData: 'Datos inválidos para eliminar usuario',
      deleteFailed: 'Error al eliminar usuario',
      operationFailed: 'Error al ejecutar operación',
    },
  },

  // Productos (ejemplo de nueva entidad)
  products: {
    title: 'Productos',
    description: 'Gestionar productos del sistema',
    create: 'Crear producto',
    edit: 'Editar producto',
    delete: 'Eliminar producto',
    empty: 'No se encontraron productos',
    loading: 'Cargando productos...',
    messages: {
      delete: {
        confirm: '¿Estás seguro de que quieres eliminar este producto?',
        success: 'Producto eliminado exitosamente',
      },
      create: {
        success: 'Producto creado exitosamente',
      },
      update: {
        success: 'Producto actualizado exitosamente',
      },
    },
    errors: {
      noPermission: 'No tienes permiso para acceder a esta página',
      noDeletePermission: 'No tienes permiso para eliminar productos',
      notFound: 'Producto no encontrado',
      invalidData: 'Datos inválidos para eliminar producto',
      deleteFailed: 'Error al eliminar producto',
      operationFailed: 'Error al ejecutar operación',
    },
  },
};

export default esTranslations;
