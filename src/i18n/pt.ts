const ptTranslations = {
  // Autenticação
  auth: {
    login: 'Entrar',
    logout: 'Sair',
    logoutAll: '<PERSON><PERSON> de todos os dispositivos',
    register: '<PERSON><PERSON>strar',
    email: 'E-mail',
    password: '<PERSON><PERSON>',
    confirmPassword: 'Confirma<PERSON> senha',
    forgotPassword: '<PERSON><PERSON><PERSON> minha senha',
    rememberMe: 'Lembrar de mim',
    loginTitle: 'Entrar na sua conta',
    registerTitle: 'Criar nova conta',
    loginButton: 'Entrar',
    registerButton: 'Cadastrar',
    invalidCredentials: 'Credenciais inválidas',
    accountCreated: 'Conta criada com sucesso',
    loginSuccess: 'Login realizado com sucesso',
    // New keys for redesigned login
    signInTitle: 'Entrar',
    signInSubtitle: 'Digite seu email e senha para acessar sua conta.',
    emailPlaceholder: 'Endereço de email',
    passwordPlaceholder: '<PERSON><PERSON>',
    createAccountText: 'Quer criar uma conta?',
    signUpLink: 'Cadastre-se',
    welcomeTitle: 'Bem-vindo à Caza Design.',
    welcomeSubtitle: 'Vamos configurar tudo para que você possa verificar sua conta pessoal e começar a configurar seu perfil',
    showPassword: 'Mostrar senha',
    hidePassword: 'Ocultar senha',
    copyrightText: '© Caza Design 2025',
  },

  // Geral
  common: {
    save: 'Salvar',
    cancel: 'Cancelar',
    delete: 'Excluir',
    edit: 'Editar',
    add: 'Adicionar',
    search: 'Pesquisar',
    loading: 'Carregando...',
    error: 'Erro',
    success: 'Sucesso',
    warning: 'Aviso',
    info: 'Informação',
    confirm: 'Confirmar',
    close: 'Fechar',
    back: 'Voltar',
    next: 'Próximo',
    previous: 'Anterior',
    yes: 'Sim',
    no: 'Não',
    name: 'Nome',
    description: 'Descrição',
    status: 'Status',
    actions: 'Ações',
    createdAt: 'Criado em',
    updatedAt: 'Atualizado em',
    notAvailable: 'N/A',
    errors: {
      invalidData: 'Dados inválidos na requisição',
      serverError: 'Erro interno do servidor',
      networkError: 'Erro de conexão. Verifique sua internet.',
      unauthorized: 'Sessão expirada. Faça login novamente.',
      forbidden: 'Você não tem permissão para esta ação',
      badRequest: 'Requisição inválida',
      conflict: 'Conflito de dados',
      tooManyRequests:
        'Muitas requisições. Tente novamente em alguns instantes.',
      serviceUnavailable: 'Serviço temporariamente indisponível',
      refreshTokenError: 'Erro ao atualizar token de autenticação',
      loadUserError: 'Erro ao carregar dados do usuário',
      loadPermissionsError: 'Erro ao carregar permissões',
      unknownError: 'Erro desconhecido',
    },
    confirmDelete: {
      title: 'Confirmar exclusão',
      description:
        'Tem certeza que deseja excluir {name}? Esta ação não pode ser desfeita.',
    },
  },

  // Formulários
  forms: {
    required: 'Este campo é obrigatório',
    emailInvalid: 'E-mail inválido',
    passwordTooShort: 'Senha deve ter pelo menos 6 caracteres',
    passwordMismatch: 'Senhas não coincidem',
    fieldRequired: 'Campo obrigatório',
  },

  // Temas
  theme: {
    light: 'Claro',
    dark: 'Escuro',
    system: 'Sistema',
    toggleTheme: 'Alternar tema',
  },

  // Idiomas
  language: {
    portuguese: 'Português',
    english: 'Inglês',
    spanish: 'Espanhol',
    selectLanguage: 'Selecionar idioma',
  },

  // Dashboard
  dashboard: {
    title: 'Dashboard',
    welcome: 'Bem-vindo',
    overview: 'Visão geral',
    recentProjects: 'Projetos recentes',
    recentClients: 'Clientes recentes',
    statistics: 'Estatísticas',
  },

  // Projetos
  projects: {
    title: 'Projetos',
    description: 'Gerenciar projetos do sistema',
    create: 'Criar projeto',
    edit: 'Editar projeto',
    delete: 'Deletar projeto',
    empty: 'Nenhum projeto encontrado',
    loading: 'Carregando projetos...',
    messages: {
      delete: {
        confirm: 'Tem certeza que deseja deletar este projeto?',
        success: 'Projeto deletado com sucesso',
      },
      create: {
        success: 'Projeto criado com sucesso',
      },
      update: {
        success: 'Projeto atualizado com sucesso',
      },
    },
    errors: {
      noPermission: 'Você não tem permissão para acessar esta página',
      noDeletePermission: 'Você não tem permissão para deletar projetos',
      notFound: 'Projeto não encontrado',
      invalidData: 'Dados inválidos para deletar projeto',
      deleteFailed: 'Erro ao deletar projeto',
      operationFailed: 'Erro ao executar operação',
    },
  },

  // Clientes
  clients: {
    title: 'Clientes',
    description: 'Gerenciar clientes do sistema',
    create: 'Criar cliente',
    edit: 'Editar cliente',
    delete: 'Deletar cliente',
    empty: 'Nenhum cliente encontrado',
    loading: 'Carregando clientes...',
    messages: {
      delete: {
        confirm: 'Tem certeza que deseja deletar este cliente?',
        success: 'Cliente deletado com sucesso',
      },
      create: {
        success: 'Cliente criado com sucesso',
      },
      update: {
        success: 'Cliente atualizado com sucesso',
      },
    },
    errors: {
      noPermission: 'Você não tem permissão para acessar esta página',
      noDeletePermission: 'Você não tem permissão para deletar clientes',
      notFound: 'Cliente não encontrado',
      invalidData: 'Dados inválidos para deletar cliente',
      deleteFailed: 'Erro ao deletar cliente',
      operationFailed: 'Erro ao executar operação',
    },
  },

  // Item genérico
  item: {
    errors: {
      noPermission: 'Você não tem permissão para acessar este recurso',
      notFound: 'Item não encontrado',
      operationFailed: 'Erro ao executar operação',
    },
  },

  // Usuários
  users: {
    title: 'Usuários',
    description: 'Gerenciar usuários do sistema',
    view: 'Ver usuários',
    create: 'Criar usuário',
    edit: 'Editar usuário',
    delete: 'Deletar usuário',
    manage: 'Gerenciar usuários do sistema',
    group: 'Grupo',
    empty: 'Nenhum usuário encontrado',
    loading: 'Carregando usuários...',
    messages: {
      delete: {
        confirm: 'Tem certeza que deseja deletar este usuário?',
        success: 'Usuário deletado com sucesso',
      },
      create: {
        success: 'Usuário criado com sucesso',
      },
      update: {
        success: 'Usuário atualizado com sucesso',
      },
    },
    errors: {
      noPermission: 'Você não tem permissão para acessar esta página',
      noDeletePermission: 'Você não tem permissão para deletar usuários',
      notFound: 'Usuário não encontrado',
      invalidData: 'Dados inválidos para deletar usuário',
      deleteFailed: 'Erro ao deletar usuário',
      operationFailed: 'Erro ao executar operação',
    },
  },

  // Produtos (exemplo de nova entidade)
  products: {
    title: 'Produtos',
    description: 'Gerenciar produtos do sistema',
    create: 'Criar produto',
    edit: 'Editar produto',
    delete: 'Deletar produto',
    empty: 'Nenhum produto encontrado',
    loading: 'Carregando produtos...',
    messages: {
      delete: {
        confirm: 'Tem certeza que deseja deletar este produto?',
        success: 'Produto deletado com sucesso',
      },
      create: {
        success: 'Produto criado com sucesso',
      },
      update: {
        success: 'Produto atualizado com sucesso',
      },
    },
    errors: {
      noPermission: 'Você não tem permissão para acessar esta página',
      noDeletePermission: 'Você não tem permissão para deletar produtos',
      notFound: 'Produto não encontrado',
      invalidData: 'Dados inválidos para deletar produto',
      deleteFailed: 'Erro ao deletar produto',
      operationFailed: 'Erro ao executar operação',
    },
  },
};

export default ptTranslations;
