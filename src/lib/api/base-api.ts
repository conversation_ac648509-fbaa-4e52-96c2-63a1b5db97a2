import axios from 'axios';

import i18n from '../i18n';
import { TokenManager } from '../token-manager';
import { authApi } from './auth-api';

// Classe para erros da API
export class ApiError extends Error {
  public status: number;
  public data: any;

  constructor(message: string, status: number, data?: any) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.data = data;
  }
}

// Configuração base do axios
export const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3003/api/v1',
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000, // 10 segundos
  withCredentials: true, // Importante para enviar cookies HttpOnly
});

// Flag para evitar múltiplas tentativas de refresh simultâneas
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value?: any) => void;
  reject: (reason?: any) => void;
}> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });

  failedQueue = [];
};

// Interceptor para adicionar token de autenticação e headers de idioma
api.interceptors.request.use(async config => {
  const token = TokenManager.getAccessToken();

  // Adicionar header de idioma baseado no i18n atual
  const currentLanguage = i18n.language || 'pt';
  config.headers['Accept-Language'] = currentLanguage;

  // Não fazer refresh proativo se já for uma requisição de refresh
  if (config.url?.includes('/auth/refresh')) {
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  }

  if (token) {
    // Verificar se o token está próximo de expirar
    if (TokenManager.isAccessTokenExpired()) {
      // Se o access token expirou, tentar refresh
      if (!isRefreshing) {
        isRefreshing = true;

        try {
          const rememberMe = TokenManager.getRememberMe();

          const authData = await authApi.refresh(rememberMe);
          TokenManager.saveAuthData(authData);
          processQueue(null, authData.token);
          config.headers.Authorization = `Bearer ${authData.token}`;
        } catch (error) {
          processQueue(error, null);
          TokenManager.clearAuthData();
          throw error;
        } finally {
          isRefreshing = false;
        }
      } else {
        // Se já está fazendo refresh, aguardar
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then(token => {
            config.headers.Authorization = `Bearer ${token}`;
            return config;
          })
          .catch(err => {
            return Promise.reject(err);
          });
      }
    } else {
      config.headers.Authorization = `Bearer ${token}`;
    }
  }

  return config;
});

// Response interceptor
api.interceptors.response.use(
  response => response,
  async error => {
    const originalRequest = error.config;

    // Se for erro 401 e não tentou refresh ainda
    const shouldTryRefresh =
      error.response?.status === 401 &&
      !originalRequest._retry &&
      !originalRequest.url?.includes('/auth/refresh') &&
      !originalRequest.url?.includes('/auth/logout') &&
      !originalRequest.url?.includes('/auth/login') &&
      window.location.pathname !== '/';

    if (shouldTryRefresh) {
      originalRequest._retry = true;

      if (isRefreshing) {
        // Se já está fazendo refresh, aguardar
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then(token => {
            originalRequest.headers.Authorization = `Bearer ${token}`;
            return api(originalRequest);
          })
          .catch(err => {
            return Promise.reject(err);
          });
      }

      isRefreshing = true;

      try {
        const rememberMe = TokenManager.getRememberMe();

        const authData = await authApi.refresh(rememberMe);
        TokenManager.saveAuthData(authData);
        processQueue(null, authData.token);

        // Retry the original request with new token
        originalRequest.headers.Authorization = `Bearer ${authData.token}`;
        return api(originalRequest);
      } catch (refreshError) {
        processQueue(refreshError, null);
        TokenManager.clearAuthData();

        // Redirect to login
        if (window.location.pathname !== '/') {
          window.location.href = '/';
        }

        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    // Se for erro de rede
    if (!error.response) {
      throw new ApiError(t('common.errors.networkError'), 0);
    }

    const { status, data } = error.response;

    // Logar apenas erros inesperados (500+) para debug
    if (status >= 500) {
      console.error('API Error (500+):', { status, data, error });
    }

    // Sempre priorizar mensagem do backend quando disponível
    const backendMessage = data?.message;
    let message: string;

    // Erro de autenticação (já tratado acima, mas mantido para outros casos)
    if (status === 401) {
      TokenManager.clearAuthData();

      // Só redirecionar se não estiver já na página de login e não for logout
      if (
        window.location.pathname !== '/' &&
        !originalRequest.url?.includes('/auth/logout')
      ) {
        window.location.href = '/';
      }

      message = backendMessage || t('common.errors.unauthorized');
    }
    // Erro de validação (422)
    else if (status === 422) {
      message = backendMessage || t('common.errors.invalidData');
    }
    // Erro de permissão (403)
    else if (status === 403) {
      message = backendMessage || t('common.errors.forbidden');
    }
    // Erro não encontrado (404)
    else if (status === 404) {
      message = backendMessage || t('common.errors.notFound');
    }
    // Erro do servidor (500+)
    else if (status >= 500) {
      message = backendMessage || t('common.errors.serverError');
    }
    // Erro de serviço indisponível (503)
    else if (status === 503) {
      message = backendMessage || t('common.errors.serviceUnavailable');
    }
    // Outros erros
    else {
      message = backendMessage || t('common.errors.serverError');
    }

    throw new ApiError(message, status, data);
  }
);

// Função utilitária para tratar erros de forma consistente
export function handleApiError(
  error: any,
  entityName: string = 'item'
): { statusCode: number; message: string } {
  // Extrair status code e mensagem do erro
  let statusCode: number = 0;
  let errorMessage: string = '';

  if (error instanceof ApiError) {
    statusCode = error.status;
    errorMessage = error.message;
  } else if (error.response) {
    // AxiosError
    statusCode = error.response.status;
    errorMessage = error.response.data?.message || '';
  } else if (error.status) {
    // Outros tipos de erro
    statusCode = error.status;
    errorMessage = error.message || '';
  }

  // Sempre priorizar mensagem do backend quando disponível
  if (errorMessage) {
    return { statusCode, message: errorMessage };
  }

  // Fallback para mensagens do frontend quando não há mensagem específica do backend
  if (statusCode === 400) {
    return { statusCode, message: t('common.errors.badRequest') };
  } else if (statusCode === 401) {
    return { statusCode, message: t('common.errors.unauthorized') };
  } else if (statusCode === 403) {
    return { statusCode, message: t(`${entityName}.errors.noPermission`) };
  } else if (statusCode === 404) {
    return { statusCode, message: t(`${entityName}.errors.notFound`) };
  } else if (statusCode === 409) {
    return { statusCode, message: t('common.errors.conflict') };
  } else if (statusCode === 422) {
    return { statusCode, message: t('common.errors.invalidData') };
  } else if (statusCode === 429) {
    return { statusCode, message: t('common.errors.tooManyRequests') };
  } else if (statusCode === 503) {
    return { statusCode, message: t('common.errors.serviceUnavailable') };
  } else if (statusCode >= 500) {
    return { statusCode, message: t('common.errors.serverError') };
  } else {
    return { statusCode, message: t(`${entityName}.errors.operationFailed`) };
  }
}

// Função helper para traduzir mensagens de erro
const t = (key: string): string => {
  return i18n.t(key);
};
