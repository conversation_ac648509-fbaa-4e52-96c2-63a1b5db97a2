import { z } from 'zod';

// Schema de validação para login
export const loginSchema = z.object({
  email: z
    .string()
    .min(1, 'forms.required')
    .email('forms.emailInvalid'),
  password: z
    .string()
    .min(1, 'forms.required')
    .min(6, 'forms.passwordTooShort'),
  rememberMe: z.boolean().optional(),
});

export type LoginFormData = z.infer<typeof loginSchema>;

// Schema de validação para registro (para uso futuro)
export const registerSchema = z.object({
  email: z
    .string()
    .min(1, 'forms.required')
    .email('forms.emailInvalid'),
  password: z
    .string()
    .min(1, 'forms.required')
    .min(6, 'forms.passwordTooShort'),
  confirmPassword: z
    .string()
    .min(1, 'forms.required'),
}).refine((data) => data.password === data.confirmPassword, {
  message: 'forms.passwordMismatch',
  path: ['confirmPassword'],
});

export type RegisterFormData = z.infer<typeof registerSchema>;
