import {
  LayoutDashboard,
  Users,
  FolderOpen,
  Package,
  UserCheck,
  Calendar,
  DollarSign,
  Settings,
  LogOut,
  MoreHorizontal,
  User,
  type LucideIcon,
} from 'lucide-react';

export interface MenuItem {
  id: string;
  label: string;
  icon: LucideIcon;
  href?: string;
  permission?: string;
  children?: MenuItem[];
  badge?: string | number;
}

export interface MenuSection {
  id: string;
  items: MenuItem[];
}

export const navigationConfig: MenuSection[] = [
  {
    id: 'main',
    items: [
      {
        id: 'dashboard',
        label: 'navigation.dashboard',
        icon: LayoutDashboard,
        href: '/dashboard',
      },
      {
        id: 'customers',
        label: 'navigation.customers',
        icon: Users,
        href: '/customers',
        permission: 'clients.view',
        children: [
          {
            id: 'customers-list',
            label: 'navigation.customersList',
            icon: Users,
            href: '/customers',
            permission: 'clients.view',
          },
          {
            id: 'customers-create',
            label: 'navigation.customersCreate',
            icon: Users,
            href: '/customers/create',
            permission: 'clients.create',
          },
        ],
      },
      {
        id: 'projects',
        label: 'navigation.projects',
        icon: FolderOpen,
        href: '/projects',
        permission: 'projects.view',
        children: [
          {
            id: 'project-registration',
            label: 'navigation.projectRegistration',
            icon: FolderOpen,
            href: '/projects/registration',
            permission: 'projects.create',
          },
          {
            id: 'planning',
            label: 'navigation.planning',
            icon: FolderOpen,
            href: '/projects/planning',
            permission: 'projects.view',
          },
          {
            id: 'environments',
            label: 'navigation.environments',
            icon: FolderOpen,
            href: '/projects/environments',
            permission: 'projects.view',
          },
          {
            id: 'execution-stages',
            label: 'navigation.executionStages',
            icon: FolderOpen,
            href: '/projects/execution',
            permission: 'projects.view',
          },
          {
            id: 'financial-management',
            label: 'navigation.financialManagement',
            icon: DollarSign,
            href: '/projects/financial',
            permission: 'projects.financial',
          },
        ],
      },
      {
        id: 'inventory',
        label: 'navigation.inventory',
        icon: Package,
        href: '/inventory',
        permission: 'products.view',
        children: [
          {
            id: 'inventory-list',
            label: 'navigation.inventoryList',
            icon: Package,
            href: '/inventory',
            permission: 'products.view',
          },
          {
            id: 'inventory-create',
            label: 'navigation.inventoryCreate',
            icon: Package,
            href: '/inventory/create',
            permission: 'products.create',
          },
        ],
      },
      {
        id: 'team',
        label: 'navigation.team',
        icon: UserCheck,
        href: '/team',
        permission: 'users.view',
        children: [
          {
            id: 'team-list',
            label: 'navigation.teamList',
            icon: UserCheck,
            href: '/team',
            permission: 'users.view',
          },
          {
            id: 'team-create',
            label: 'navigation.teamCreate',
            icon: UserCheck,
            href: '/team/create',
            permission: 'users.create',
          },
        ],
      },
      {
        id: 'calendar',
        label: 'navigation.calendar',
        icon: Calendar,
        href: '/calendar',
        children: [
          {
            id: 'calendar-view',
            label: 'navigation.calendarView',
            icon: Calendar,
            href: '/calendar',
          },
          {
            id: 'calendar-events',
            label: 'navigation.calendarEvents',
            icon: Calendar,
            href: '/calendar/events',
          },
        ],
      },
      {
        id: 'financials',
        label: 'navigation.financials',
        icon: DollarSign,
        href: '/financials',
        permission: 'financial.view',
        children: [
          {
            id: 'financials-overview',
            label: 'navigation.financialsOverview',
            icon: DollarSign,
            href: '/financials',
            permission: 'financial.view',
          },
          {
            id: 'financials-reports',
            label: 'navigation.financialsReports',
            icon: DollarSign,
            href: '/financials/reports',
            permission: 'financial.reports',
          },
        ],
      },
    ],
  },
  {
    id: 'bottom',
    items: [
      {
        id: 'more',
        label: 'navigation.more',
        icon: MoreHorizontal,
      },
      {
        id: 'users',
        label: 'navigation.users',
        icon: User,
        href: '/users',
        permission: 'users.view',
      },
      {
        id: 'settings',
        label: 'navigation.settings',
        icon: Settings,
        href: '/settings',
      },
      {
        id: 'logout',
        label: 'navigation.logout',
        icon: LogOut,
      },
    ],
  },
];

export const getMenuItemById = (id: string): MenuItem | undefined => {
  for (const section of navigationConfig) {
    for (const item of section.items) {
      if (item.id === id) return item;
      if (item.children) {
        const child = item.children.find((child) => child.id === id);
        if (child) return child;
      }
    }
  }
  return undefined;
};

export const getParentMenuItem = (childId: string): MenuItem | undefined => {
  for (const section of navigationConfig) {
    for (const item of section.items) {
      if (item.children?.some((child) => child.id === childId)) {
        return item;
      }
    }
  }
  return undefined;
};
