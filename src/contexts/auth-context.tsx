'use client';

import {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';

import { useTranslation } from '@/hooks/use-translation';
import { ApiError, authApi, permissionManager } from '@/lib/api';
import { TokenManager } from '@/lib/token-manager';
import { User } from '@/lib/types';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  login: (
    email: string,
    password: string,
    rememberMe?: boolean
  ) => Promise<void>;
  logout: () => void;
  logoutAll: () => void;
  hasPermission: (permission: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasAllPermissions: (permissions: string[]) => boolean;
  isPermissionValid: (permission: string) => boolean;
}

interface AuthProviderProps {
  children: ReactNode;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: AuthProviderProps) {
  const { t } = useTranslation();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeAuth = async () => {
      setError(null);

      if (TokenManager.isLoggedIn()) {
        try {
          const response = await authApi.me();
          setUser(response);
          await permissionManager.initialize(response);
        } catch (error) {
          if (error instanceof ApiError && error.status === 401) {
            // Limpar todos os dados de autenticação em caso de 401
            TokenManager.clearAuthData();
            setUser(null);
            permissionManager.clearCache();
          }
          let msg = t('common.errors.serverError');
          if (error instanceof Error && error.message) msg = error.message;
          if (typeof error === 'string') msg = error;
          setError(msg);
        }
      }

      setLoading(false);
    };

    initializeAuth();
  }, [t]);

  const login = useCallback(
    async (email: string, password: string, rememberMe?: boolean) => {
      setError(null);
      try {
        const authData = await authApi.login(email, password, rememberMe);

        // Armazenar dados de autenticação usando TokenManager
        TokenManager.saveAuthData(authData);

        // Usar dados do usuário da resposta do login
        setUser(authData.user);
        await permissionManager.initialize(authData.user);
      } catch (error) {
        // Preservar a mensagem de erro original do backend
        if (error instanceof ApiError) {
          setError(error.message);
          throw error;
        }

        let msg = t('common.errors.serverError');
        if (error instanceof Error && error.message) msg = error.message;
        if (typeof error === 'string') msg = error;
        setError(msg);
        throw new ApiError(msg, 0);
      }
    },
    [t]
  );

  const logout = useCallback(async () => {
    try {
      await authApi.logout();
    } catch (_error) {
      // Ignorar erros no logout, sempre limpar dados locais
    } finally {
      // Limpar dados de autenticação
      TokenManager.clearAuthData();
      setUser(null);
      permissionManager.clearCache();
    }
  }, []);

  const logoutAll = useCallback(async () => {
    try {
      await authApi.logoutAll();
    } catch (_error) {
      // Ignorar erros no logout, sempre limpar dados locais
    } finally {
      // Limpar dados de autenticação
      TokenManager.clearAuthData();
      setUser(null);
      permissionManager.clearCache();
    }
  }, []);

  // Métodos de permissão usando o PermissionManager
  const hasPermission = (permission: string): boolean =>
    permissionManager.hasPermission(permission);
  const hasAnyPermission = (permissions: string[]): boolean =>
    permissionManager.hasAnyPermission(permissions);
  const hasAllPermissions = (permissions: string[]): boolean =>
    permissionManager.hasAllPermissions(permissions);
  const isPermissionValid = (permission: string): boolean =>
    permissionManager.isPermissionValid(permission);

  const value: AuthContextType = {
    user,
    loading,
    error,
    login,
    logout,
    logoutAll,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    isPermissionValid,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
