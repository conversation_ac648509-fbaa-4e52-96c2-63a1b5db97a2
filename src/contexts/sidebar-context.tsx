'use client';

import {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';

import { useLocalStorage } from '@/hooks/use-local-storage';

export type SidebarState = 'collapsed' | 'expanded' | 'hover';

interface SidebarContextType {
  // Sidebar state
  state: SidebarState;
  isCollapsed: boolean;
  isExpanded: boolean;
  isHovering: boolean;
  
  // Actions
  expand: () => void;
  collapse: () => void;
  toggle: () => void;
  setHover: (hovering: boolean) => void;
  
  // Active menu tracking
  activeItem: string | null;
  activeParent: string | null;
  setActiveItem: (itemId: string | null) => void;
  setActiveParent: (parentId: string | null) => void;
  
  // Submenu visibility
  openSubmenus: Set<string>;
  toggleSubmenu: (itemId: string) => void;
  openSubmenu: (itemId: string) => void;
  closeSubmenu: (itemId: string) => void;
  closeAllSubmenus: () => void;
}

interface SidebarProviderProps {
  children: ReactNode;
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

export function SidebarProvider({ children }: SidebarProviderProps) {
  // Persistent sidebar state
  const [isExpanded, setIsExpanded] = useLocalStorage('sidebar-expanded', false);
  const [isHovering, setIsHovering] = useState(false);
  
  // Active menu tracking
  const [activeItem, setActiveItem] = useState<string | null>(null);
  const [activeParent, setActiveParent] = useState<string | null>(null);
  
  // Submenu visibility
  const [openSubmenus, setOpenSubmenus] = useState<Set<string>>(new Set());

  // Determine current state
  const state: SidebarState = isExpanded 
    ? 'expanded' 
    : isHovering 
    ? 'hover' 
    : 'collapsed';

  const isCollapsed = state === 'collapsed';

  // Actions
  const expand = useCallback(() => {
    setIsExpanded(true);
  }, [setIsExpanded]);

  const collapse = useCallback(() => {
    setIsExpanded(false);
    setIsHovering(false);
  }, [setIsExpanded]);

  const toggle = useCallback(() => {
    if (isExpanded) {
      collapse();
    } else {
      expand();
    }
  }, [isExpanded, expand, collapse]);

  const setHover = useCallback((hovering: boolean) => {
    if (!isExpanded) {
      setIsHovering(hovering);
    }
  }, [isExpanded]);

  // Submenu management
  const toggleSubmenu = useCallback((itemId: string) => {
    setOpenSubmenus(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        newSet.add(itemId);
      }
      return newSet;
    });
  }, []);

  const openSubmenu = useCallback((itemId: string) => {
    setOpenSubmenus(prev => new Set(prev).add(itemId));
  }, []);

  const closeSubmenu = useCallback((itemId: string) => {
    setOpenSubmenus(prev => {
      const newSet = new Set(prev);
      newSet.delete(itemId);
      return newSet;
    });
  }, []);

  const closeAllSubmenus = useCallback(() => {
    setOpenSubmenus(new Set());
  }, []);

  // Auto-close submenus when sidebar collapses
  useEffect(() => {
    if (isCollapsed && !isHovering) {
      closeAllSubmenus();
    }
  }, [isCollapsed, isHovering, closeAllSubmenus]);

  const value: SidebarContextType = {
    // State
    state,
    isCollapsed,
    isExpanded,
    isHovering,
    
    // Actions
    expand,
    collapse,
    toggle,
    setHover,
    
    // Active menu tracking
    activeItem,
    activeParent,
    setActiveItem,
    setActiveParent,
    
    // Submenu visibility
    openSubmenus,
    toggleSubmenu,
    openSubmenu,
    closeSubmenu,
    closeAllSubmenus,
  };

  return (
    <SidebarContext.Provider value={value}>
      {children}
    </SidebarContext.Provider>
  );
}

export function useSidebar() {
  const context = useContext(SidebarContext);
  if (context === undefined) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  return context;
}
